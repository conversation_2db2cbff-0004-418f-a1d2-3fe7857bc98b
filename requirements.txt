# Core ML libraries
torch>=2.1.0
transformers>=4.45.0
datasets>=2.14.0
tokenizers>=0.15.0

# Training and optimization
trl>=0.11.0
peft>=0.12.0
accelerate>=0.25.0
bitsandbytes>=0.41.0

# Flash Attention for efficiency
flash-attn>=2.5.0

# Utilities
numpy>=1.24.0
scipy>=1.10.0
scikit-learn>=1.3.0

# Logging and monitoring
tensorboard>=2.14.0
wandb>=0.15.0

# Data processing
pandas>=2.0.0
pyarrow>=12.0.0

# System utilities
psutil>=5.9.0
GPUtil>=1.4.0

# Optional: For better performance
ninja>=1.11.0
packaging>=21.0

# HuggingFace Hub
huggingface-hub>=0.19.0

# For CUDA operations
nvidia-ml-py3>=7.352.0
